# - name: Create namespace
#   shell: kubectl create namespace static-file-server --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -
---
- name: Create static-file-server namespace
  kubernetes.core.k8s:
    name: static-file-server
    api_version: v1
    kind: Namespace
    state: present
    context: "{{ kubectl_context }}"

# - name: Create pv and pvc
#   shell: kubectl apply --context={{kubectl_context}} -f -
#   args:
#     stdin: "{{lookup('template', '{{apps_dir}}/ks-static-file-server/templates/ks-static-file-server-pv-pvc.yml')}}"

- name: Create pv and pvc
  kubernetes.core.k8s:
    definition: "{{ lookup('template', apps_dir + '/ks-static-file-server/templates/ks-static-file-server-pv-pvc.yml') | from_yaml_all | list }}"
    state: present
    context: "{{ kubectl_context }}"


# - name: Setup ingress
#   shell: kubectl apply -f -
#   args:
#     stdin: "{{ lookup('template', apps_dir + '/ks-static-file-server/templates/ks-static-file-server-ingress.yml')}}"
- name: Setup ingress
  kubernetes.core.k8s:
    definition: "{{ lookup('template', apps_dir + '/ks-static-file-server/templates/ks-static-file-server-ingress.yml') | from_yaml }}"
    state: present
    context: "{{ kubectl_context }}"
    continue_on_error: true
