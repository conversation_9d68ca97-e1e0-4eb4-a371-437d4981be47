---
- name: Create pv directory
  file:
    path: "{{cluster_data_dir}}/redmine-pv"
    state: directory
  when: dir_creation_supported == true

# - name: Create namespace
#   shell: kubectl create namespace redmine --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Create redmine namespace
  kubernetes.core.k8s:
    name: redmine
    api_version: v1
    kind: Namespace
    state: present
    context: "{{ kubectl_context }}"

# - name: Create local persistent volume
#   shell: kubectl apply --context={{kubectl_context}} --namespace redmine -f -
#   args:
#     stdin: "{{ lookup('template', '{{apps_dir}}/redmine/templates/redmine-pv-pvc.yml')}}"

- name: Create local persistent volume
  kubernetes.core.k8s:
    definition: "{{ lookup('template', apps_dir + '/redmine/templates/redmine-pv-pvc.yml') | from_yaml_all | list }}"
    state: present
    context: "{{ kubectl_context }}"

- name: Create creds secret
  kubernetes.core.k8s:
    state: present
    context: "{{ kubectl_context }}"
    definition:
      apiVersion: v1
      kind: Secret
      metadata:
        name: redmine-creds
        namespace: redmine
      type: Opaque
      data:
        redmine-password: "{{ 'password' | b64encode }}"

- name: Create db creds secret
  kubernetes.core.k8s:
    state: present
    context: "{{ kubectl_context }}"
    definition:
      apiVersion: v1
      kind: Secret
      metadata:
        name: redmine-db-creds
        namespace: redmine
      type: Opaque
      data:
        user: "{{ 'bn_redmine' | b64encode }}"
        password: "{{ 'password' | b64encode }}"

# - name: Setup ingress
#   shell: kubectl apply --context={{kubectl_context}} -f -
#   args:
#     stdin: "{{ lookup('template', '{{apps_dir}}/redmine/templates/redmine2-ingress.yml')}}"

- name: Setup ingress
  kubernetes.core.k8s:
    definition: "{{ lookup('template', apps_dir + '/redmine/templates/redmine2-ingress.yml') | from_yaml }}"
    state: present
    context: "{{ kubectl_context }}"
    continue_on_error: true


- name: Deploy redmine
  shell: helm install redmine --kube-context={{kubectl_context}} --namespace redmine -f - oci://registry-1.docker.io/bitnamicharts/redmine --version 32.1.6
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/redmine/templates/redmine-overrides.yml')}}"
