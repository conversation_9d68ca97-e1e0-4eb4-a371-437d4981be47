---
- name: Create pv directory
  file:
    path: "{{cluster_data_dir}}/redmine-pv"
    state: directory
  when: dir_creation_supported == true

# - name: Create namespace
#   shell: kubectl create namespace redmine --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Create redmine namespace
  kubernetes.core.k8s:
    name: redmine
    api_version: v1
    kind: Namespace
    state: present
    context: "{{ kubectl_context }}"

- name: Create local persistent volume
  shell: kubectl apply --context={{kubectl_context}} --namespace redmine -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/redmine/templates/redmine-pv-pvc.yml')}}"

- name: Create local persistent volume
  kubernetes.core.k8s:
    definition: "{{ lookup('template', db_dir + '/postgres/templates/postgres-pv-pvc.yml') | from_yaml_all | list }}"
    state: present
    context: "{{ kubectl_context }}"



- name: Create creds secret
  shell: kubectl create secret generic -n redmine redmine-creds --context={{kubectl_context}} --from-literal=redmine-password='password'
  ignore_errors: true

- name: Create db creds secret
  shell: kubectl create secret generic -n redmine redmine-db-creds --context={{kubectl_context}} --from-literal=user=bn_redmine --from-literal=password='password'
  ignore_errors: true

- name: Setup ingress
  shell: kubectl apply --context={{kubectl_context}} -f -
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/redmine/templates/redmine2-ingress.yml')}}"

- name: Deploy redmine
  shell: helm install redmine --kube-context={{kubectl_context}} --namespace redmine -f - oci://registry-1.docker.io/bitnamicharts/redmine --version 32.1.6
  args:
    stdin: "{{ lookup('template', '{{apps_dir}}/redmine/templates/redmine-overrides.yml')}}"
