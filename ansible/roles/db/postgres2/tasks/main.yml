# - name: Create namespace
#   shell: kubectl create namespace postgres --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

- name: Create postgres namespace
  kubernetes.core.k8s:
    name: postgres
    api_version: v1
    kind: Namespace
    state: present
    context: "{{ kubectl_context }}"

# - name: Create local persistent volume (postgres pv)
#   shell: kubectl apply --context={{kubectl_context}} --namespace postgres -f -
#   args:
#     stdin: "{{ lookup('template', '{{db_dir}}/postgres/templates/postgres-pv-pvc.yml')}}"

- name: Create local persistent volume (postgres pv)
  kubernetes.core.k8s:
    definition: "{{ lookup('template', db_dir + '/postgres/templates/postgres-pv-pvc.yml') | from_yaml_all | list }}"
    state: present
    context: "{{ kubectl_context }}"

# # postgres version 16.4.15
# - name: Deploy postgres
#   shell: helm install postgres --kube-context={{kubectl_context}} --namespace postgres -f - oci://registry-1.docker.io/bitnamicharts/postgresql --version 16.5.0
#   args:
#     stdin: "{{ lookup('template', '{{db_dir}}/postgres2/templates/postgres-overrides.yml')}}"

- name: Deploy Postgres
  kubernetes.core.helm:
    name: postgres
    chart_ref: oci://registry-1.docker.io/bitnamicharts/postgresql
    chart_version: 16.5.0
    kube_context: "{{ kubectl_context }}"
    release_namespace: postgres
    values: "{{ lookup('template', db_dir + '/postgres2/templates/postgres-overrides.yml') | from_yaml }}"
    disable_hook: true
