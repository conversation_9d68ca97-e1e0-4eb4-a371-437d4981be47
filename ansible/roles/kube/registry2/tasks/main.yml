# - name: Create namespace
#   shell: kubectl create namespace registry --context={{kubectl_context}} --dry-run=client -o yaml | kubectl apply -f -

# - name: Create local persistent volume
#   shell: kubectl apply --context={{kubectl_context}} --namespace registry -f -
#   args:
#     stdin: "{{lookup('template', '{{registry_templates_dir}}/docker-registry-pv-pvc.yml')}}"

# - name: Add twuni repo
#   shell: helm repo add twuni https://helm.twun.io

# - name: Deploy registry
#   shell: helm install docker-registry --kube-context={{kubectl_context}} --namespace registry -f - twuni/docker-registry --version 2.2.3
#   args:
#     stdin: "{{lookup('file', '{{registry_templates_dir}}/docker-registry-overrides.yml')}}"

# - name: Setup ingress
#   shell: kubectl apply --context={{kubectl_context}} -f -
#   args:
#     stdin: "{{ lookup('template', '{{registry_templates_dir}}/docker-registry-ingress.yml')}}"
---

- name: Create registry namespace
  kubernetes.core.k8s:
    name: registry
    api_version: v1
    kind: Namespace
    state: present
    context: "{{ kubectl_context }}"

- name: Create persistent volume and claim for registry
  kubernetes.core.k8s:
    definition: "{{ lookup('template', registry_templates_dir + '/docker-registry-pv-pvc.yml') | from_yaml_all | list }}"
    state: present
    context: "{{ kubectl_context }}"

- name: Add twuni Helm repository
  kubernetes.core.helm_repository:
    name: twuni
    repo_url: https://helm.twun.io
    state: present

- name: Deploy docker-registry chart
  kubernetes.core.helm:
    name: docker-registry
    chart_ref: twuni/docker-registry
    release_namespace: registry
    kubeconfig: "{{ kubeconfig_path | default(omit) }}"
    kube_context: "{{ kubectl_context }}"
    chart_version: 2.2.3
    values: "{{ lookup('file', registry_templates_dir + '/docker-registry-overrides.yml') | from_yaml }}"
    state: present
    disable_hook: true

- name: Setup registry ingress
  kubernetes.core.k8s:
    definition: "{{ lookup('template', registry_templates_dir + '/docker-registry-ingress.yml') | from_yaml }}"
    state: present
    context: "{{ kubectl_context }}"
