# - name: Add ingress-nginx repo
#   shell: helm repo add --kube-context={{kubectl_context}} ingress-nginx https://kubernetes.github.io/ingress-nginx

# - name: Install ingress-nginx
#   shell: helm install ingress-nginx --kube-context={{kubectl_context}} --namespace ingress-nginx --create-namespace -f - ingress-nginx/ingress-nginx --version 4.12.0
#   args:
#     stdin: "{{ lookup('template', '{{ingress_nginx_templates_dir}}/ingress-nginx-overrides.yml')}}"

- name: Add ingress-nginx Helm repository
  kubernetes.core.helm_repository:
    name: ingress-nginx
    repo_url: https://kubernetes.github.io/ingress-nginx
    state: present

- name: Install ingress-nginx chart
  kubernetes.core.helm:
    name: ingress-nginx
    chart_ref: ingress-nginx/ingress-nginx
    release_namespace: ingress-nginx
    kube_context: "{{ kubectl_context }}"
    chart_version: 4.12.0
    values: "{{ lookup('template', ingress_nginx_templates_dir + '/ingress-nginx-overrides.yml') | from_yaml }}"
    state: present
    disable_hook: true

# - pause:
#     seconds: 30

# - name: Setup ks web ingress
#   shell: kubectl apply --context={{kubectl_context}} -f -
#   args:
#     stdin: "{{ lookup('template', '{{ingress_nginx_templates_dir}}/ks-web-ingress.yml')}}"

# - name: Setup ks web ingress
#   shell: kubectl apply --context={{kubectl_context}} --validate=false -f -
#   args:
#     stdin: "{{ lookup('template', ingress_nginx_templates_dir + '/ks-web-ingress.yml')}}"

- name: Setup ks web ingress
  kubernetes.core.k8s:
    state: present
    definition: "{{ lookup('template', ingress_nginx_templates_dir + '/ks-web-ingress.yml')}}"
    validate:
      fail_on_error: false
      strict: true
