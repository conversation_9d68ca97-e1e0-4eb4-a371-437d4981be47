---
km_name: "km"
kubectl_context: "admin@talos-default-11"

top_level_domain: "biz"
domain_name: "fernir"
domain: "{{domain_name}}.{{top_level_domain}}"

cluster_environment: "staging"

# todo fd delete later when old ubuntu way is archived
dir_creation_supported: false

# Kubernetes
kube_major_version_number: 1.32
kube_version_number: "{{kube_major_version_number}}.0"
kube_version: v{{kube_version_number}}

internal_gateway_ip: *********

load_balancer_base_ip: "10.5."
load_balancer_network: "{{load_balancer_base_ip}}0.0"
load_balancer_network_mask: "***********"

volume_xs: 500Mi
volume_s: 1.5Gi
volume_m: 3Gi
volume_l: 5Gi
volume_xl: 8Gi
volume_xxl: 10Gi
volume_xxxl: 10Gi
volume_xxxxl: 10Gi

cluster_data_dir: /var/mnt/cluster-data

base_dir: ../..
sources_dir: "{{base_dir}}/cluster"
ansible_dir: "{{sources_dir}}/ansible"
roles_dir: "{{ansible_dir}}/roles"
kube_dir: "{{roles_dir}}/kube"
commons_dir: "{{roles_dir}}/commons"
dotfiles_dir: "{{commons_dir}}/dotfiles"
ci_cd_dir: "{{roles_dir}}/ci-cd"
apps_dir: "{{roles_dir}}/apps"
db_dir: "{{roles_dir}}/db"
vms_dir: "{{home_dir}}/vms"
security_dir: "{{roles_dir}}/security"
ops_dir: "{{roles_dir}}/ops"
monitoring_dir: "{{ops_dir}}/monitoring"

secrets_dir: "{{base_dir}}/secrets"
secrets_ansible_dir: "{{secrets_dir}}/ansible"
secrets_roles_dir: "{{secrets_ansible_dir}}/roles"
secrets_security_dir: "{{secrets_dir}}/security"

postgresql_password_dev: password
postgres_port: 31003

## old not used
ansible_host: "{{km_ip}}"
node_ip: "{{ansible_host}}"

node_hostname: "{{km_name}}"
node_name: "{{km_name}}"

ansible_user: "km"
node_user: "km"

node_password: "ubuntu"

registry_ip: "{{km_ip}}"
registry: "{{registry_ip}}:30500"
http_registry: "http://{{registry}}"

network_interface: "enp0s8"
ansible_ssh_pass: "{{node_password}}"
ansible_become_pass: "{{node_password}}"

node_domain: "cl.fernir.biz"

service_cidr: "********/12"
pod_network_cidr: "********/16"

insecure_registries: ["{{node_ip}}:30500"]

kubeadmin_config: /etc/kubernetes/admin.conf
kube_addon_dir: /etc/kubernetes/addon

hdd: hdd
hdd_dir: "/mnt/{{hdd}}"
ssd2: ssd2
ssd2_dir: "/mnt/{{ssd2}}"
safe_dir: "{{hdd_dir}}/safe"

home_dir: "/home/<USER>"

cluster_ops_data_dir: /usr/src/cluster-ops-data

fast_cluster_data_dir: /usr/src/fast-cluster-data

openldap_ip: "{{load_balancer_base_ip}}0.3"
openldap_tls_secret_name: "openldap-tls"
ldap_admin_password: k64Nut!PWC72

app_name: "saas-kit"
app_db_name: "{{app_name}}-{{cluster_environment}}"

local_registry_address: "*************:5000"
