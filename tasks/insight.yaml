---
# Preinstall Sleep
- name: Stop and disable MOMS tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $checkPath = "{{ item.check_path | default(true) | lower }}"
    $serverName = "{{ item.server | default('moms_app_server') }}"

    # Set task path based on check_path flag
    if ($checkPath -eq "true") {
      $taskPath = "\\$serverName\C$\\MOMS\\ScheduledTasks\\$taskName"
      if (-not (Test-Path $taskPath)) {
        Write-Host "Marker path not found for $taskName on $serverName, skipping..."
        exit 0
      }
    } else {
      $taskPath = "\\"
    }

    # Get and manage the scheduled task
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      try {
        Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
        Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
        Write-Host "Successfully stopped and disabled task: $taskName on $serverName"
      } catch {
        Write-Warning "Failed to stop/disable task $taskName`: $_"
      }
    } else {
      Write-Host "Task not found: $taskName on $serverName"
    }
  when: "{{ item.condition | default(true) | lower }}"
  delegate_to: "{{ item.server | default('moms_app_server') }}"
  loop:
    - name: "MOMSWorkOrderMessagingTask"
      condition: work_order_messaging_feature
    - name: "MOMSFailureAnalysisTaskConsole"
    - name: "MOMSInventoryLevelNotification"
    - name: "MOMSNotificationEscalationTask"
    - name: "MOMSPredictiveMaintenanceNotificationTaskConsole"
    - name: "MOMSPreventiveMaintenanceTask"
    - name: "TransPortal_MOMSEventLoader"
    - name: "CPS_MOMSEventLoader"
    - name: "VOTT_MOMSEventLoader"
    - name: "SolarWinds_MOMSEventLoader"
    - name: "WhatsUp_MOMSEventLoader"
    - name: "MOMSEventLoader"
    - name: "ImageReview_MOMSEventLoader"
    - name: "MOMSExternalNotifier"
    - name: "MOMSTrafficDataLoader"
    - name: "MOMSActiveDirectoryPolling"
      condition: ad_polling_task
      check_path: false
    - name: "TransPortalActiveDirectoryPollingTask"
      server: "{{ transportal_web_alias }}"
      condition: "{{ not active_directory_feature and deployment_type == 'PRODUCT' }}"
      check_path: false
    - name: "Integrity_MOMSEventLoader"
      server: "{{ integrity_event_loader_exe_server }}"
      condition: integrity_event_loader
      check_path: false
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Stop MOMS Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: item.condition
  delegate_to: "{{ moms_app_server }}"
  async: 1000
  poll: 5
  loop:
    - service: "MOMSMobileService"
      condition: mobile_feature
    - service: "MOMSService"
      condition: true
    - service: "TransSuiteMOMSService"
      condition: trans_suite_feature
    - service: "InfinityMOMSService"
      condition: infinity_feature
    - service: "IntegrityMOMSService"
      condition: integrity_feature
    - service: "MOMSGPSLocatorService"
      condition: gps_feature
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Stop IIS Web
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: "{{ item.condition | default(true) | lower }}"
  delegate_to: "{{ item.server | default('moms_app_server') }}"
  async: 1000
  poll: 5
  loop:
    - service: "World Wide Web Publishing Service"
    - service: "Net.Tcp Listener Adapter"
    - service: "NetTcpActivator"
    - service: "Windows Process Activation Service"
    - service: "World Wide Web Publishing Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Net.Tcp Listener Adapter"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "NetTcpActivator"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Windows Process Activation Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
  loop_control:
    label: "{{ item.service }}"
  tags:
    - insight

# TODO
- name: Reboot the Windows machines
  ansible.windows.win_reboot:
    reboot_timeout: 600
    test_command: "whoami"
  delegate_to: "{{ item }}"
  when: item.condition
  loop:
    - moms_app_server
    - insight_mobile_server
  loop_control:
    label: "{{ item }}"
  tags:
    - insight

# Postinstall Startup
# TODO
- name: Update the Release version and date in the TransPortal DB
  ansible.windows.win_shell: |
    sqlcmd -b -U "{{ db_user }}" -P "{{ db_password }}" -Q
    "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer')
    BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''{{ insight.release_version }}'',
    dtReleaseDate=''{{ insight.dated_deployment_dir }}'' where iAppID=852' END" -S
    "{{ transportal_db_listener }}" -d SSO
  delegate_to: "{{ transportal_db_server }}"
  when: transportal_feature
  tags:
    - insight

- name: Start MOMS and IIS Web services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  when: "{{ item.condition | default(true) | lower }}"
  delegate_to: "{{ item.server | default('moms_app_server') }}"
  async: 1000
  poll: 5
  loop:
    - service: "MOMSMobileService"
      condition: mobile_feature
    - service: "MOMSService"
    - service: "TransSuiteMOMSService"
      condition: trans_suite_feature
    - service: "InfinityMOMSService"
      condition: infinity_feature
    - service: "IntegrityMOMSService"
      condition: integrity_feature
    - service: "MOMSGPSLocatorService"
      condition: gps_feature
    - service: "ASP.NET State Service"
    - service: "World Wide Web Publishing Service"
    - service: "Net.Tcp Listener Adapter"
    - service: "NetTcpActivator"
    - service: "Windows Process Activation Service"
    - service: "World Wide Web Publishing Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Net.Tcp Listener Adapter"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "NetTcpActivator"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Windows Process Activation Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
  loop_control:
    label: "{{ item.service }}"
  tags:
    - insight

- name: Enable MOMS Tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $checkPath = "{{ item.check_path | default(true) | lower }}"
    $serverName = "{{ item.server | default('moms_app_server') }}"

    # Set task path based on check_path flag
    if ($checkPath -eq "true") {
      $taskPath = "\\$serverName\C$\\MOMS\\ScheduledTasks\\$taskName"
      if (-not (Test-Path $taskPath)) {
        Write-Host "Marker path not found for $taskName on $serverName, skipping..."
        exit 0
      }
    } else {
      $taskPath = "\\"
    }

    # Get and manage the scheduled task
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      try {
        Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
        Write-Host "Successfully enabled task: $taskName on $serverName"
      } catch {
        Write-Warning "Failed to enable task $taskName`: $_"
      }
    } else {
      Write-Host "Task not found: $taskName on $serverName"
    }
  when: "{{ item.condition | default(true) | lower }} and {{ deployment_type }} not in ('DEV', 'QA')"
  delegate_to: "{{ item.server | default('moms_app_server') }}"
  loop:
    - name: "MOMSWorkOrderMessagingTask"
      condition: work_order_messaging_feature
    - name: "MOMSFailureAnalysisTaskConsole"
    - name: "MOMSInventoryLevelNotification"
    - name: "MOMSNotificationEscalationTask"
      condition: "and {{ deployment_environment }} != 'PRODUCT'"
    - name: "MOMSPredictiveMaintenanceNotificationTaskConsole"
    - name: "MOMSPreventiveMaintenanceTask"
    - name: "TransPortal_MOMSEventLoader"
    - name: "CPS_MOMSEventLoader"
    - name: "VOTT_MOMSEventLoader"
    - name: "SolarWinds_MOMSEventLoader"
    - name: "WhatsUp_MOMSEventLoader"
    - name: "MOMSEventLoader"
    - name: "ImageReview_MOMSEventLoader"
    - name: "MOMSExternalNotifier"
    - name: "MOMSTrafficDataLoader"
    - name: "Integrity_MOMSEventLoader"
      server: "{{ integrity_event_loader_exe_server }}"
      condition: integrity_event_loader
      check_path: false
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Enable MOMS Tasks
  ansible.windows.win_shell: |
    $taskName = "MOMSActiveDirectoryPolling"
    $taskPath = "\\"
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      Start-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
    }
  when: ad_polling_task
  delegate_to: "{{ moms_app_server }}"
  tags:
    - insight
