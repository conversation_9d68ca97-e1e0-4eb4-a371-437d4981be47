---
- name: Execute tasks on localhost
  hosts: localhost
  connection: local
  vars:
    xname: test
    flag1: true
    flag2: false
  tasks:
    - name: Run role
      debug:
        msg: "{{ item.name}}"
      when: item.condition
      loop:
        - name: "1111MOMSWorkOrderMessagingTask"
          condition: flag1 and flag2
        - name: "{{xname}}"
          condition: flag1
      loop_control:
        label: "{{item.name}}"
        # - name: "MOMSInventoryLevelNotification"
        # - name: "MOMSNotificationEscalationTask"
        # - name: "MOMSPredictiveMaintenanceNotificationTaskConsole"
        # - name: "MOMSPreventiveMaintenanceTask"
        # - name: "TransPortal_MOMSEventLoader"
        # - name: "CPS_MOMSEventLoader"
        # - name: "VOTT_MOMSEventLoader"
        # - name: "SolarWinds_MOMSEventLoader"
        # - name: "WhatsUp_MOMSEventLoader"
        # - name: "MOMSEventLoader"
        # - name: "ImageReview_MOMSEventLoader"
        # - name: "MOMSExternalNotifier"
        # - name: "MOMSTrafficDataLoader"
        # - name: "2222MOMSActiveDirectoryPolling"
        #   condition: false
